import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/savings_progress_card.dart';
import '../../widgets/stats_card.dart';
import '../../widgets/trial_card.dart';
import '../../theme/app_theme.dart';
import '../../services/auth_service.dart';
import '../../services/trial_service.dart';
import '../../models/trial.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with SingleTickerProviderStateMixin {
  late String _greeting;
  late String _userName;
  List<Trial> _activeTrials = [];
  List<Trial> _expiringTrials = [];
  TrialStats? _stats;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _initializeData();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _initializeData() {
    final user = AuthService.getCurrentUser();
    _userName = user?.name ?? 'User';
    _greeting = _getGreeting();
    _loadTrialData();
  }

  void _loadTrialData() {
    setState(() {
      _activeTrials = TrialService.getActiveTrials();
      _expiringTrials = TrialService.getExpiringTrials(7);
      _stats = TrialService.getTrialStats();
    });
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  }

  @override
  Widget build(BuildContext context) {
    final user = AuthService.getCurrentUser();
    final stats = _stats;

    return Scaffold(
      appBar: GreetingAppBar(
        userName: _userName,
        greeting: _greeting,
        notificationCount: _expiringTrials.length,
        onProfileTap: () => Navigator.pushNamed(context, '/profile'),
        onNotificationTap: () => Navigator.pushNamed(context, '/notifications'),
      ),
      body: RefreshIndicator(
        color: AppTheme.primaryGreen,
        onRefresh: () async {
          await Future.delayed(const Duration(milliseconds: 500));
          _loadTrialData();
        },
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: AppSpacing.lg),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Savings Progress Card
                  if (user != null)
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
                      child: SavingsProgressCard(
                        currentSavings: stats?.monthlySavings ?? 0,
                        goalAmount: user.monthlySavingsGoal,
                        currency: user.currency,
                        onTap: () => Navigator.pushNamed(context, '/savings'),
                      ),
                    ),

                  const SizedBox(height: AppSpacing.lg),

                  // Quick Stats
                  if (stats != null) ...[
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
                      child: Text(
                        'Quick Stats',
                        style: AppTextStyles.heading3.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(height: AppSpacing.sm),
                    QuickStatsRow(
                      activeTrials: stats.activeTrials.toString(),
                      expiringThisWeek: stats.expiringThisWeek.toString(),
                      monthlySavings: stats.monthlySavings.toStringAsFixed(0),
                      totalSavings: stats.totalSavings.toStringAsFixed(0),
                      onActiveTrialsTap: () => Navigator.pushNamed(context, '/active-trials'),
                      onExpiringTap: () => Navigator.pushNamed(context, '/expiring-trials'),
                      onMonthlySavingsTap: () => Navigator.pushNamed(context, '/monthly-savings'),
                      onTotalSavingsTap: () => Navigator.pushNamed(context, '/total-savings'),
                    ),
                  ],

                  const SizedBox(height: AppSpacing.xl),

                  // Expiring Trials Section
                  if (_expiringTrials.isNotEmpty) ...[
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
                      child: Row(
                        children: [
                          Icon(
                            Icons.warning_amber_rounded,
                            color: AppTheme.warningOrange,
                            size: 24,
                          ),
                          const SizedBox(width: AppSpacing.sm),
                          Text(
                            'Expiring Soon',
                            style: AppTextStyles.heading3.copyWith(
                              color: AppTheme.warningOrange,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          TextButton(
                            onPressed: () => Navigator.pushNamed(context, '/expiring-trials'),
                            style: TextButton.styleFrom(
                              foregroundColor: AppTheme.primaryGreen,
                            ),
                            child: const Text('View All'),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: AppSpacing.sm),
                    SizedBox(
                      height: 220,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
                        itemCount: _expiringTrials.length,
                        itemBuilder: (context, index) {
                          final trial = _expiringTrials[index];
                          return Padding(
                            padding: const EdgeInsets.only(right: AppSpacing.sm),
                            child: SizedBox(
                              width: 300,
                              child: TrialCard(
                                trial: trial,
                                onTap: () => _showTrialDetails(trial),
                                onCancel: () => _cancelTrial(trial),
                                onSnooze: () => _snoozeTrial(trial),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    const SizedBox(height: AppSpacing.xl),
                  ],

                  // Active Trials Section
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
                    child: Row(
                      children: [
                        Text(
                          'Active Trials',
                          style: AppTextStyles.heading3.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Spacer(),
                        TextButton(
                          onPressed: () => Navigator.pushNamed(context, '/active-trials'),
                          style: TextButton.styleFrom(
                            foregroundColor: AppTheme.primaryGreen,
                          ),
                          child: const Text('View All'),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: AppSpacing.sm),

                  // Active Trials List
                  if (_activeTrials.isEmpty)
                    _buildEmptyState()
                  else
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: _activeTrials.take(5).length,
                      itemBuilder: (context, index) {
                        final trial = _activeTrials[index];
                        return Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSpacing.md,
                            vertical: AppSpacing.xs,
                          ),
                          child: TrialCard(
                            trial: trial,
                            onTap: () => _showTrialDetails(trial),
                            onCancel: () => _cancelTrial(trial),
                            onSnooze: () => _snoozeTrial(trial),
                          ),
                        );
                      },
                    ),

                  const SizedBox(height: AppSpacing.xxl),
                ],
              ),
            ),
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => Navigator.pushNamed(context, '/add-trial'),
        icon: const Icon(Icons.add),
        label: const Text('Add Trial'),
        backgroundColor: AppTheme.primaryGreen,
        foregroundColor: Colors.white,
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.lg),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Column(
        children: [
          Icon(
            Icons.subscriptions_outlined,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            'No Active Trials',
            style: AppTextStyles.heading3.copyWith(
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            'Add your first trial to start tracking and saving money!',
            style: AppTextStyles.bodyMedium.copyWith(
              color: Colors.grey.shade500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSpacing.lg),
          ElevatedButton.icon(
            onPressed: () => Navigator.pushNamed(context, '/add-trial'),
            icon: const Icon(Icons.add),
            label: const Text('Add Your First Trial'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryGreen,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: AppSpacing.lg,
                vertical: AppSpacing.sm,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showTrialDetails(Trial trial) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: const BorderRadius.vertical(
              top: Radius.circular(AppBorderRadius.xl),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: Column(
            children: [
              // Drag handle
              Container(
                margin: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // Content
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.all(AppSpacing.lg),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Trial header
                      Row(
                        children: [
                          Container(
                            width: 60,
                            height: 60,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(AppBorderRadius.md),
                              color: Colors.grey.shade100,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.05),
                                  blurRadius: 5,
                                ),
                              ],
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(AppBorderRadius.md),
                              child: trial.logoUrl != null
                                  ? Image.network(
                                      trial.logoUrl!,
                                      fit: BoxFit.cover,
                                      errorBuilder: (context, error, stackTrace) =>
                                          const Icon(
                                            Icons.subscriptions,
                                            color: AppTheme.textSecondaryLight,
                                          ),
                                    )
                                  : const Icon(
                                      Icons.subscriptions,
                                      color: AppTheme.textSecondaryLight,
                                    ),
                            ),
                          ),
                          const SizedBox(width: AppSpacing.md),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  trial.serviceName,
                                  style: AppTextStyles.heading2.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: AppSpacing.xs),
                                Text(
                                  trial.serviceCategory,
                                  style: AppTextStyles.bodyMedium.copyWith(
                                    color: AppTheme.textSecondary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: AppSpacing.lg),

                      // Trial details
                      _buildDetailRow('Start Date', DateFormat('MMM d, y').format(trial.startDate)),
                      _buildDetailRow('Trial Duration', '${trial.trialDurationDays} days'),
                      _buildDetailRow('Expires', DateFormat('MMM d, y').format(trial.expiryDate)),
                      _buildDetailRow('Days Remaining', '${trial.daysRemaining} days'),
                      _buildDetailRow('Monthly Cost', '${trial.currency}${trial.monthlyCost.toStringAsFixed(2)}'),

                      if (trial.notes != null && trial.notes!.isNotEmpty) ...[
                        const SizedBox(height: AppSpacing.lg),
                        Text(
                          'Notes',
                          style: AppTextStyles.heading3.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: AppSpacing.sm),
                        Container(
                          padding: const EdgeInsets.all(AppSpacing.sm),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade50,
                            borderRadius: BorderRadius.circular(AppBorderRadius.sm),
                          ),
                          child: Text(
                            trial.notes!,
                            style: AppTextStyles.bodyMedium,
                          ),
                        ),
                      ],

                      const SizedBox(height: AppSpacing.xl),

                      // Action buttons
                      Row(
                        children: [
                          Expanded(
                            child: OutlinedButton(
                              onPressed: () {
                                Navigator.pop(context);
                                _snoozeTrial(trial);
                              },
                              style: OutlinedButton.styleFrom(
                                foregroundColor: AppTheme.primaryGreen,
                                side: const BorderSide(color: AppTheme.primaryGreen),
                                padding: const EdgeInsets.symmetric(vertical: AppSpacing.md),
                              ),
                              child: const Text('Snooze'),
                            ),
                          ),
                          const SizedBox(width: AppSpacing.md),
                          Expanded(
                            flex: 2,
                            child: ElevatedButton(
                              onPressed: () {
                                Navigator.pop(context);
                                _cancelTrial(trial);
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppTheme.urgentRed,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(vertical: AppSpacing.md),
                              ),
                              child: const Text('Cancel Trial'),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppSpacing.xs),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppTheme.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _cancelTrial(Trial trial) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Cancel ${trial.serviceName}',
          style: AppTextStyles.heading3.copyWith(fontWeight: FontWeight.bold),
        ),
        content: Text(
          'Are you sure you want to cancel ${trial.serviceName}? This will save you ${trial.currency}${trial.monthlyCost.toStringAsFixed(2)}/month.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            style: TextButton.styleFrom(foregroundColor: AppTheme.textSecondary),
            child: const Text('Keep Trial'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.urgentRed,
              foregroundColor: Colors.white,
            ),
            child: const Text('Cancel Trial'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await TrialService.cancelTrial(trial.id);
      _loadTrialData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${trial.serviceName} trial cancelled! You saved ${trial.currency}${trial.monthlyCost.toStringAsFixed(2)}/month.',
            ),
            backgroundColor: AppTheme.successGreen,
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(AppSpacing.md),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppBorderRadius.sm),
            ),
          ),
        );
      }
    }
  }

  Future<void> _snoozeTrial(Trial trial) async {
    final snoozeDays = await showDialog<int>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Snooze ${trial.serviceName}',
          style: AppTextStyles.heading3.copyWith(fontWeight: FontWeight.bold),
        ),
        content: const Text('How many days would you like to snooze this reminder?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(foregroundColor: AppTheme.textSecondary),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, 1),
            child: const Text('1 Day'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, 3),
            child: const Text('3 Days'),
          ),
          TextButton(
            onPressed: () => Navigator.pushNamed(context, '/add-trial'),
            child: const Text('1 Week'),
          ),
        ],
      ),
    );

    if (snoozeDays != null) {
      await TrialService.snoozeTrialReminder(trial.id, snoozeDays);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${trial.serviceName} reminder snoozed for $snoozeDays day${snoozeDays == 1 ? '' : 's'}.'),
            backgroundColor: AppTheme.primaryGreen,
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(AppSpacing.md),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppBorderRadius.sm),
            ),
          ),
        );
      }
    }
  }
}